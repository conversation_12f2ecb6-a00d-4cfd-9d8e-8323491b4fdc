<?php
/**
 * Página de Administração do Custom New User Email Plugin
 */

if (!defined('ABSPATH')) {
    exit;
}

// Verificar permissões
if (!current_user_can('manage_options')) {
    wp_die('Você não tem permissão para acessar esta página.');
}

$nonce = wp_create_nonce('cnue_admin_nonce');

// Obter configurações atuais
$settings = [
    'email_enabled' => get_option('cnue_email_enabled', '0'),
    'email_subject' => get_option('cnue_email_subject', 'Bem-vindo! Seus dados de acesso'),
    'email_from_name' => get_option('cnue_email_from_name', get_bloginfo('name')),
    'email_from_email' => get_option('cnue_email_from_email', get_option('admin_email')),
    'email_header_color' => get_option('cnue_email_header_color', '#4a90e2'),
    'email_header_pattern' => get_option('cnue_email_header_pattern', 'none'),
    'email_button_color' => get_option('cnue_email_button_color', '#4a90e2'),
    'email_greeting' => get_option('cnue_email_greeting', 'Olá'),
    'email_main_text' => get_option('cnue_email_main_text', 'Sua conta foi criada com sucesso em {site_name}! Abaixo estão seus dados de acesso:'),
    'email_credentials_title' => get_option('cnue_email_credentials_title', 'Seus dados de acesso:'),
    'email_username_label' => get_option('cnue_email_username_label', 'Usuário:'),
    'email_password_label' => get_option('cnue_email_password_label', 'Senha:'),
    'email_login_url_label' => get_option('cnue_email_login_url_label', 'Link de acesso:'),
    'email_instruction_text' => get_option('cnue_email_instruction_text', 'Clique no botão abaixo para fazer login e começar a usar sua conta:'),
    'email_button_text' => get_option('cnue_email_button_text', 'Fazer Login'),
    'email_security_note' => get_option('cnue_email_security_note', 'Por segurança, recomendamos que você altere sua senha após o primeiro login.'),
    'email_footer_text' => get_option('cnue_email_footer_text', 'Este é um email automático, não responda.'),
    'email_template_html' => get_option('cnue_email_template_html', '')
];
?>

<style>
/* Custom New User Email Admin Styles - Copiado do Custom Password Reset */

.cpr-admin-wrap {
    max-width: 100%;
    margin: 0;
    padding: 0;
    background: #f8fafc;
    min-height: 100vh;
}

/* Header clean e minimalista */
.cpr-admin-header {
    background: white;
    border: none;
    border-radius: 0;
    padding: 0;
    margin: 0;
    border-bottom: 1px solid #e2e8f0;
    position: relative;
}

.cpr-header-content {
    position: relative;
    z-index: 2;
    padding: 32px 60px;
}

.cpr-header-title {
    display: flex;
    align-items: center;
    gap: 20px;
}

.cpr-icon {
    width: 48px;
    height: 48px;
    background: #f8fafc;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e2e8f0;
}

.cpr-icon .dashicons {
    font-size: 24px;
    color: #64748b;
    width: 24px;
    height: 24px;
}

.cpr-title-text h1 {
    margin: 0 0 4px 0;
    font-size: 24px;
    font-weight: 600;
    color: #1e293b;
    line-height: 1.2;
}

.cpr-title-text p {
    margin: 0;
    font-size: 14px;
    color: #64748b;
    font-weight: 400;
}

/* Container principal */
.cpr-admin-container {
    background: white;
    border: none;
    border-radius: 0;
    margin: 0;
    box-shadow: none;
    overflow: hidden;
}

/* Navigation Tabs - Design moderno */
.cpr-nav-tabs {
    background: white;
    border-bottom: 1px solid #e2e8f0;
    margin: 0;
    padding: 0;
    position: sticky;
    top: 32px;
    z-index: 100;
}

.cpr-nav-wrapper {
    display: flex;
    margin: 0 auto;
    padding: 0 60px;
    gap: 0;
}

.cpr-nav-tab {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 24px;
    text-decoration: none;
    color: #64748b;
    font-weight: 500;
    font-size: 14px;
    border-bottom: 3px solid transparent;
    transition: all 0.2s ease;
    position: relative;
    background: none;
    border-left: none;
    border-right: none;
    border-top: none;
}

.cpr-nav-tab:hover {
    color: #475569;
    background: #f8fafc;
    text-decoration: none;
}

.cpr-nav-tab.cpr-nav-tab-active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
    background: white;
}

.cpr-tab-icon {
    font-size: 16px;
    line-height: 1;
}

.cpr-tab-text {
    font-weight: 500;
}

/* Tab Content - Layout moderno */
.cpr-tab-content {
    padding: 60px;
    min-height: 600px;
    background: #f8fafc;
    display: none;
}

.cpr-tab-content.active {
    display: block;
}

/* Grid de configurações */
.cpr-settings-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 32px;
}

.cpr-settings-grid .cpr-setting-group.full-width {
    grid-column: 1 / -1;
}

.cpr-settings-grid .cpr-setting-group.priority {
    order: -1;
}

.cpr-setting-group {
    background: white;
    border-radius: 12px;
    padding: 28px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    height: fit-content;
}

.cpr-setting-group.full-width {
    padding: 32px;
}

.cpr-setting-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e2e8f0;
}

.cpr-setting-group.full-width .cpr-setting-header {
    margin-bottom: 32px;
}

.cpr-setting-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Setting Items - Design moderno */
.cpr-setting-item {
    margin-bottom: 24px;
}

.cpr-setting-item:last-child {
    margin-bottom: 0;
}

.cpr-setting-group.full-width .cpr-setting-item {
    margin-bottom: 28px;
}

.cpr-setting-label {
    display: block;
    font-weight: 600;
    font-size: 14px;
    color: #374151;
    margin-bottom: 8px;
}

.cpr-setting-desc {
    display: block;
    font-size: 13px;
    color: #6b7280;
    margin-top: 6px;
    line-height: 1.5;
}

/* Inputs modernos */
.cpr-input, .cpr-select {
    width: 100%;
    max-width: 400px;
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    transition: all 0.2s ease;
    color: #374151;
}

.cpr-input:focus, .cpr-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.cpr-input::placeholder {
    color: #9ca3af;
}

/* Input Group */
.cpr-input-group {
    display: flex;
    gap: 12px;
    align-items: center;
}

.cpr-input-group .cpr-input {
    flex: 1;
}

/* Toggle Switch moderno */
.cpr-toggle-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
}

.cpr-toggle {
    position: relative;
    display: inline-block;
    width: 48px;
    height: 24px;
}

.cpr-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.cpr-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #cbd5e1;
    transition: 0.3s;
    border-radius: 24px;
}

.cpr-toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.cpr-toggle input:checked + .cpr-toggle-slider {
    background-color: #3b82f6;
}

.cpr-toggle input:checked + .cpr-toggle-slider:before {
    transform: translateX(24px);
}

.cpr-toggle-text {
    font-size: 14px;
    color: #374151;
    font-weight: 500;
}

/* Conditional Fields */
.cpr-conditional {
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.cpr-conditional.show {
    display: block;
    opacity: 1;
}

/* Botões modernos */
.cpr-btn-primary, .cpr-btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;
    border: none;
    outline: none;
}

.cpr-btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.cpr-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
    color: white;
}

.cpr-btn-secondary {
    background: white;
    color: #374151;
    border: 1px solid #d1d5db;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.cpr-btn-secondary:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    transform: translateY(-1px);
    color: #374151;
}

.cpr-btn-icon {
    font-size: 16px;
    line-height: 1;
}

/* Action Buttons Container */
.cpr-admin-actions {
    background: white;
    border-top: 1px solid #e2e8f0;
    padding: 0;
    position: fixed;
    bottom: 0;
    right: 0;
    left: 160px; /* Espaço para o menu lateral expandido */
    z-index: 100;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
}

/* Garantir que o sticky funcione apenas dentro do wrap do plugin */
.cpr-admin-wrap {
    position: relative;
}

/* Garantir espaço para o rodapé do WordPress */
body.wp-admin {
    padding-bottom: 100px !important;
}

/* Ajustar o rodapé do WordPress para aparecer acima dos botões */
#wpfooter {
    margin-bottom: 80px !important;
    z-index: 50;
    position: relative;
}

.cpr-actions-wrapper {
    margin: 0 auto;
    padding: 24px 60px;
    display: flex;
    align-items: center;
    gap: 16px;
}

/* Color Picker */
.cpr-color-picker {
    max-width: 100px !important;
}

/* Preview Actions */
.cpr-preview-actions {
    display: flex;
    gap: 20px;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.cpr-test-email-wrapper {
    display: flex;
    gap: 12px;
    align-items: center;
}

.cpr-email-preview {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    min-height: 400px;
    padding: 20px;
}

.cpr-preview-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #6b7280;
}

.cpr-status-info {
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.cpr-status-label {
    color: #6b7280;
    font-weight: 500;
}

.cpr-status-value {
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
}

.cpr-status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.cpr-status-dot.active {
    background: #10b981;
}

.cpr-status-dot.inactive {
    background: #e5e7eb;
}

/* Melhorar labels com ícones */
.cpr-setting-label {
    display: flex;
    align-items: center;
    gap: 8px;
}

.cpr-setting-label .dashicons {
    color: #6b7280;
    font-size: 16px;
}

/* Ajustes para o WordPress Admin */
@media screen and (min-width: 783px) {
    /* Quando o menu lateral está expandido */
    body.folded .cpr-admin-actions {
        left: 36px; /* Menu colapsado */
    }

    body:not(.folded) .cpr-admin-actions {
        left: 160px; /* Menu expandido */
    }
}

@media screen and (max-width: 782px) {
    /* Mobile - menu não interfere */
    .cpr-admin-actions {
        left: 0;
    }
}

/* Responsividade */
@media (max-width: 1200px) {
    .cpr-nav-wrapper,
    .cpr-tab-content,
    .cpr-actions-wrapper {
        padding-left: 30px;
        padding-right: 30px;
    }
}

@media (max-width: 1024px) {
    .cpr-settings-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }
}

@media (max-width: 768px) {
    .cpr-nav-wrapper {
        flex-direction: column;
        padding: 0;
    }

    .cpr-nav-tab {
        border-bottom: 1px solid #e2e8f0;
        border-right: none;
    }

    .cpr-tab-content {
        padding: 30px 20px;
    }

    .cpr-settings-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .cpr-preview-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .cpr-test-email-wrapper {
        justify-content: center;
    }

    .cpr-actions-wrapper {
        flex-direction: column;
        gap: 12px;
    }

    .cpr-status-info {
        margin-left: 0;
        justify-content: center;
    }
}

.cpr-status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.cpr-status-label {
    font-size: 13px;
    color: #6c757d;
}

.cpr-status-value {
    font-size: 13px;
    font-weight: 600;
}

.cpr-info-item {
    margin-bottom: 15px;
    padding: 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.cpr-info-item strong {
    color: #2c3e50;
    display: block;
    margin-bottom: 5px;
}

.cpr-info-item code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    color: #e83e8c;
}

.cpr-preview-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    align-items: center;
}

.cpr-test-email-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.cpr-test-email-group .cpr-input {
    width: 100%;
}

.cpr-test-email-group .cpr-btn-primary {
    align-self: flex-start;
}

.cpr-test-email-input {
    padding: 10px 15px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 14px;
    width: 200px;
}

.cpr-email-preview {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    min-height: 400px;
    background: #f8f9fa;
    overflow: auto;
}

.cpr-preview-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    color: #6c757d;
}

.cpr-preview-icon {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.cpr-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cpr-loading-content {
    background: white;
    padding: 30px;
    border-radius: 8px;
    text-align: center;
}

.cpr-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.cpr-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 6px;
    color: white;
    font-weight: 600;
    z-index: 10000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
}

.cpr-notification.show {
    transform: translateX(0);
}

.cpr-notification-success {
    background: #28a745;
}

.cpr-notification-error {
    background: #dc3545;
}

/* Modal Preview */
.cpr-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 10000;
    display: none;
    align-items: center;
    justify-content: center;
}

.cpr-modal-overlay.show {
    display: flex;
}

.cpr-modal {
    background: white;
    border-radius: 12px;
    max-width: 90vw;
    max-height: 90vh;
    width: 800px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    position: relative;
}

.cpr-modal-header {
    background: white;
    color: #1e293b;
    padding: 20px 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #e2e8f0;
}

.cpr-modal-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #1e293b;
}

.cpr-modal-title .dashicons {
    color: #64748b;
    font-size: 16px;
}

.cpr-modal-close {
    background: none;
    border: none;
    color: #64748b;
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cpr-modal-close:hover {
    background: #f1f5f9;
    color: #374151;
}

.cpr-modal-body {
    padding: 0;
    max-height: calc(90vh - 80px);
    overflow-y: auto;
}

.cpr-modal-preview {
    padding: 20px;
    background: #f8fafc;
    min-height: 400px;
}

.cpr-modal-preview iframe {
    width: 100%;
    height: 500px;
    border: none;
    border-radius: 8px;
    background: white;
}

/* Template Editor Styles */
.cpr-template-editor-wrapper {
    border: 1px solid #d1d5db;
    border-radius: 8px;
    overflow: hidden;
    background: white;
}

.cpr-template-toolbar {
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    padding: 12px 16px;
    display: flex;
    gap: 12px;
    align-items: center;
}

.cpr-template-editor {
    width: 100%;
    border: none;
    padding: 16px;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 13px;
    line-height: 1.5;
    background: white;
    color: #1e293b;
    resize: vertical;
    min-height: 400px;
}

.cpr-template-editor:focus {
    outline: none;
    box-shadow: inset 0 0 0 2px #3b82f6;
}

.cpr-template-help {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 24px;
}

.cpr-help-section {
    margin-bottom: 24px;
}

.cpr-help-section:last-child {
    margin-bottom: 0;
}

.cpr-help-section h4 {
    color: #1e293b;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.cpr-help-section .dashicons {
    color: #3b82f6;
    font-size: 16px;
}

.cpr-help-section ol,
.cpr-help-section ul {
    margin-left: 20px;
    color: #4a5568;
    font-size: 13px;
    line-height: 1.6;
}

.cpr-help-section li {
    margin-bottom: 6px;
}

.cpr-help-section code {
    background: #e2e8f0;
    color: #1e293b;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

/* Tab Navigation Enhancement */
.cpr-nav-tab {
    transition: all 0.2s ease;
}

.cpr-nav-tab:not(.cpr-nav-tab-active):hover {
    background: #f1f5f9;
}

@media (max-width: 1200px) {
    .cpr-container {
        flex-direction: column;
    }

    .cpr-sidebar {
        width: 100%;
        border-left: none;
        border-top: 1px solid #e9ecef;
    }

    .cpr-form-grid {
        grid-template-columns: 1fr;
    }

    .cpr-template-toolbar {
        flex-wrap: wrap;
        gap: 8px;
    }

    .cpr-template-editor {
        font-size: 12px;
    }
}
</style>



<div class="wrap cpr-admin-wrap">
    <div class="cpr-admin-header">
        <div class="cpr-header-content">
            <div class="cpr-header-title">
                <div class="cpr-icon">
                    <i class="dashicons dashicons-email-alt"></i>
                </div>
                <div class="cpr-title-text">
                    <h1>Email Novo Usuário</h1>
                    <p>Personalize o email de criação de novo usuário do WordPress</p>
                </div>
            </div>
        </div>
    </div>

    <div class="cpr-admin-container">
        <!-- Navegação por abas -->
        <nav class="cpr-nav-tabs">
            <div class="cpr-nav-wrapper">
                <a href="#configuracoes" class="cpr-nav-tab cpr-nav-tab-active" data-tab="configuracoes">
                    <span class="cpr-tab-icon dashicons dashicons-admin-generic"></span>
                    <span class="cpr-tab-text">Configurações de Email</span>
                </a>
                <a href="#template" class="cpr-nav-tab" data-tab="template">
                    <span class="cpr-tab-icon dashicons dashicons-editor-code"></span>
                    <span class="cpr-tab-text">Template do Email</span>
                </a>
            </div>
        </nav>

        <!-- Formulário principal -->
        <form id="cnue-settings-form" method="post">
            <?php wp_nonce_field('cnue_admin_nonce', 'nonce'); ?>

            <!-- Aba Configurações -->
            <div class="cpr-tab-content active" id="tab-configuracoes">
                <div class="cpr-settings-grid">
                    <!-- Configurações Básicas - Prioridade -->
                    <div class="cpr-setting-group full-width priority">
                        <div class="cpr-setting-header">
                            <h3><span class="dashicons dashicons-admin-settings"></span> Configurações Básicas</h3>
                        </div>

                        <div class="cpr-setting-item">
                            <label class="cpr-setting-label">
                                <span class="dashicons dashicons-yes-alt"></span>
                                Ativar Email Personalizado
                            </label>
                            <div class="cpr-toggle-wrapper">
                                <label class="cpr-toggle">
                                    <input type="checkbox" name="settings[email_enabled]" id="email_enabled" value="1" <?php checked($settings['email_enabled'], '1'); ?>>
                                    <span class="cpr-toggle-slider"></span>
                                </label>
                                <span class="cpr-toggle-text">Substituir o email padrão do WordPress</span>
                            </div>
                            <span class="cpr-setting-desc">Ative para usar o template personalizado e moderno</span>
                        </div>
                    </div>

                    <!-- Configurações do Email -->
                    <div class="cpr-setting-group">
                        <div class="cpr-setting-header">
                            <h3><span class="dashicons dashicons-email-alt"></span> Dados do Email</h3>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_subject" class="cpr-setting-label">
                                <span class="dashicons dashicons-format-status"></span>
                                Assunto do Email
                            </label>
                            <input type="text" name="settings[email_subject]" id="email_subject" value="<?php echo esc_attr($settings['email_subject']); ?>" class="cpr-input" placeholder="Bem-vindo! Seus dados de acesso">
                            <span class="cpr-setting-desc">Linha de assunto que o usuário verá na caixa de entrada</span>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_from_name" class="cpr-setting-label">
                                <span class="dashicons dashicons-admin-users"></span>
                                Nome do Remetente
                            </label>
                            <input type="text" name="settings[email_from_name]" id="email_from_name" value="<?php echo esc_attr($settings['email_from_name']); ?>" class="cpr-input" placeholder="<?php echo esc_attr(get_bloginfo('name')); ?>">
                            <span class="cpr-setting-desc">Nome que aparecerá como remetente</span>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_from_email" class="cpr-setting-label">
                                <span class="dashicons dashicons-email"></span>
                                Email do Remetente
                            </label>
                            <input type="email" name="settings[email_from_email]" id="email_from_email" value="<?php echo esc_attr($settings['email_from_email']); ?>" class="cpr-input" placeholder="<?php echo esc_attr(get_option('admin_email')); ?>">
                            <span class="cpr-setting-desc">Endereço de email que aparecerá como remetente</span>
                        </div>
                    </div>

                    <!-- Personalização Visual -->
                    <div class="cpr-setting-group">
                        <div class="cpr-setting-header">
                            <h3><span class="dashicons dashicons-admin-appearance"></span> Aparência do Email</h3>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_header_color" class="cpr-setting-label">
                                <span class="dashicons dashicons-art"></span>
                                Cor do Cabeçalho
                            </label>
                            <input type="text" name="settings[email_header_color]" id="email_header_color" value="<?php echo esc_attr($settings['email_header_color']); ?>" class="cpr-color-picker">
                            <span class="cpr-setting-desc">Cor de fundo do cabeçalho do email</span>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_header_pattern" class="cpr-setting-label">
                                <span class="dashicons dashicons-admin-customizer"></span>
                                Padrão Decorativo
                            </label>
                            <select name="settings[email_header_pattern]" id="email_header_pattern" class="cpr-select">
                                <option value="none" <?php selected($settings['email_header_pattern'], 'none'); ?>>Nenhum</option>
                                <option value="floating_dots" <?php selected($settings['email_header_pattern'], 'floating_dots'); ?>>Pontos Flutuantes</option>
                                <option value="organic_shapes" <?php selected($settings['email_header_pattern'], 'organic_shapes'); ?>>Formas Orgânicas</option>
                                <option value="geometric_minimal" <?php selected($settings['email_header_pattern'], 'geometric_minimal'); ?>>Geométrico Minimal</option>
                                <option value="flowing_lines" <?php selected($settings['email_header_pattern'], 'flowing_lines'); ?>>Linhas Fluidas</option>
                                <option value="scattered_elements" <?php selected($settings['email_header_pattern'], 'scattered_elements'); ?>>Elementos Espalhados</option>
                                <option value="modern_grid" <?php selected($settings['email_header_pattern'], 'modern_grid'); ?>>Grade Moderna</option>
                                <option value="abstract_art" <?php selected($settings['email_header_pattern'], 'abstract_art'); ?>>Arte Abstrata</option>
                            </select>
                            <span class="cpr-setting-desc">Padrão decorativo que aparecerá no fundo do cabeçalho</span>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_button_color" class="cpr-setting-label">
                                <span class="dashicons dashicons-button"></span>
                                Cor do Botão de Acesso
                            </label>
                            <input type="text" name="settings[email_button_color]" id="email_button_color" value="<?php echo esc_attr($settings['email_button_color']); ?>" class="cpr-color-picker">
                            <span class="cpr-setting-desc">Cor do botão "Acessar Minha Conta"</span>
                        </div>
                    </div>

                    <!-- Conteúdo do Email -->
                    <div class="cpr-setting-group">
                        <div class="cpr-setting-header">
                            <h3><span class="dashicons dashicons-edit"></span> Conteúdo da Mensagem</h3>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_greeting" class="cpr-setting-label">
                                <span class="dashicons dashicons-format-chat"></span>
                                Saudação Inicial
                            </label>
                            <input type="text" name="settings[email_greeting]" id="email_greeting" value="<?php echo esc_attr($settings['email_greeting']); ?>" class="cpr-input" placeholder="Olá">
                            <span class="cpr-setting-desc">Como cumprimentar o novo usuário</span>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_main_text" class="cpr-setting-label">
                                <span class="dashicons dashicons-text-page"></span>
                                Mensagem Principal
                            </label>
                            <textarea name="settings[email_main_text]" id="email_main_text" rows="3" class="cpr-input" placeholder="Sua conta foi criada com sucesso em {site_name}! Abaixo estão seus dados de acesso:"><?php echo esc_textarea($settings['email_main_text']); ?></textarea>
                            <span class="cpr-setting-desc">Mensagem de boas-vindas. Use <code>{site_name}</code> para incluir o nome do site</span>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_credentials_title" class="cpr-setting-label">
                                <span class="dashicons dashicons-lock"></span>
                                Título dos Dados de Acesso
                            </label>
                            <input type="text" name="settings[email_credentials_title]" id="email_credentials_title" value="<?php echo esc_attr($settings['email_credentials_title']); ?>" class="cpr-input" placeholder="Seus dados de acesso:">
                            <span class="cpr-setting-desc">Título que aparece antes dos dados de login</span>
                        </div>

                    </div>

                    <!-- Labels dos Dados de Acesso -->
                    <div class="cpr-setting-group">
                        <div class="cpr-setting-header">
                            <h3><span class="dashicons dashicons-admin-network"></span> Labels dos Dados</h3>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_username_label" class="cpr-setting-label">
                                <span class="dashicons dashicons-admin-users"></span>
                                Label do Nome de Usuário
                            </label>
                            <input type="text" name="settings[email_username_label]" id="email_username_label" value="<?php echo esc_attr($settings['email_username_label']); ?>" class="cpr-input" placeholder="Usuário:">
                            <span class="cpr-setting-desc">Texto que aparece antes do nome de usuário</span>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_password_label" class="cpr-setting-label">
                                <span class="dashicons dashicons-lock"></span>
                                Label da Senha
                            </label>
                            <input type="text" name="settings[email_password_label]" id="email_password_label" value="<?php echo esc_attr($settings['email_password_label']); ?>" class="cpr-input" placeholder="Senha:">
                            <span class="cpr-setting-desc">Texto que aparece antes da senha</span>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_login_url_label" class="cpr-setting-label">
                                <span class="dashicons dashicons-admin-links"></span>
                                Label do Link de Acesso
                            </label>
                            <input type="text" name="settings[email_login_url_label]" id="email_login_url_label" value="<?php echo esc_attr($settings['email_login_url_label']); ?>" class="cpr-input" placeholder="Link de acesso:">
                            <span class="cpr-setting-desc">Texto que aparece antes do link de login</span>
                        </div>
                    </div>

                    <!-- Textos Adicionais -->
                    <div class="cpr-setting-group">
                        <div class="cpr-setting-header">
                            <h3><span class="dashicons dashicons-format-quote"></span> Textos Complementares</h3>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_instruction_text" class="cpr-setting-label">
                                <span class="dashicons dashicons-info"></span>
                                Instruções de Acesso
                            </label>
                            <textarea name="settings[email_instruction_text]" id="email_instruction_text" rows="2" class="cpr-input" placeholder="Clique no botão abaixo para fazer login e começar a usar sua conta:"><?php echo esc_textarea($settings['email_instruction_text']); ?></textarea>
                            <span class="cpr-setting-desc">Instruções sobre como acessar a conta</span>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_button_text" class="cpr-setting-label">
                                <span class="dashicons dashicons-button"></span>
                                Texto do Botão
                            </label>
                            <input type="text" name="settings[email_button_text]" id="email_button_text" value="<?php echo esc_attr($settings['email_button_text']); ?>" class="cpr-input" placeholder="Acessar Minha Conta">
                            <span class="cpr-setting-desc">Texto que aparece no botão de acesso</span>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_security_note" class="cpr-setting-label">
                                <span class="dashicons dashicons-shield"></span>
                                Nota de Segurança
                            </label>
                            <textarea name="settings[email_security_note]" id="email_security_note" rows="2" class="cpr-input" placeholder="Por segurança, recomendamos que você altere sua senha após o primeiro login."><?php echo esc_textarea($settings['email_security_note']); ?></textarea>
                            <span class="cpr-setting-desc">Dica de segurança para o usuário</span>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_footer_text" class="cpr-setting-label">
                                <span class="dashicons dashicons-editor-help"></span>
                                Texto do Rodapé
                            </label>
                            <input type="text" name="settings[email_footer_text]" id="email_footer_text" value="<?php echo esc_attr($settings['email_footer_text']); ?>" class="cpr-input" placeholder="Este é um email automático, não responda.">
                            <span class="cpr-setting-desc">Texto que aparece no final do email</span>
                        </div>
                    </div>

                    <!-- Teste de Email -->
                    <div class="cpr-setting-group">
                        <div class="cpr-setting-header">
                            <h3><span class="dashicons dashicons-email-alt"></span> Teste de Email</h3>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="test-email" class="cpr-setting-label">
                                <span class="dashicons dashicons-admin-users"></span>
                                Enviar Email de Teste
                            </label>
                            <div class="cpr-test-email-group">
                                <input type="email" id="test-email" placeholder="<EMAIL>" class="cpr-input">
                                <button type="button" id="send-test-email" class="cpr-btn-primary">
                                    <span class="dashicons dashicons-email-alt"></span>
                                    Enviar Teste
                                </button>
                            </div>
                            <span class="cpr-setting-desc">Digite um endereço de email para receber um exemplo do email personalizado</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Aba Template do Email -->
            <div class="cpr-tab-content" id="tab-template">
                <div class="cpr-settings-grid">
                    <!-- Editor de Template HTML -->
                    <div class="cpr-setting-group full-width">
                        <div class="cpr-setting-header">
                            <h3><span class="dashicons dashicons-editor-code"></span> Editor de Template HTML</h3>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="email_template_html" class="cpr-setting-label">
                                <span class="dashicons dashicons-media-code"></span>
                                Template HTML Personalizado
                            </label>
                            <div class="cpr-template-editor-wrapper">
                                <div class="cpr-template-toolbar">
                                    <button type="button" id="load-default-template" class="cpr-btn-secondary">
                                        <span class="dashicons dashicons-download"></span>
                                        Carregar Template Padrão
                                    </button>
                                    <button type="button" id="reset-template" class="cpr-btn-secondary">
                                        <span class="dashicons dashicons-update"></span>
                                        Resetar Template
                                    </button>
                                    <button type="button" id="validate-template" class="cpr-btn-secondary">
                                        <span class="dashicons dashicons-yes-alt"></span>
                                        Validar HTML
                                    </button>
                                </div>
                                <textarea name="settings[email_template_html]" id="email_template_html" rows="25" class="cpr-template-editor" placeholder="Cole aqui o HTML do seu template personalizado..."><?php echo esc_textarea($settings['email_template_html']); ?></textarea>
                            </div>
                            <span class="cpr-setting-desc">
                                <strong>Variáveis disponíveis:</strong><br>
                                <code>{user_login}</code> - Nome de usuário<br>
                                <code>{user_email}</code> - Email do usuário<br>
                                <code>{user_password}</code> - Senha do usuário<br>
                                <code>{user_first_name}</code> - Primeiro nome<br>
                                <code>{site_name}</code> - Nome do site<br>
                                <code>{site_url}</code> - URL do site<br>
                                <code>{login_url}</code> - URL de login<br>
                                <code>{header_color}</code> - Cor do cabeçalho<br>
                                <code>{button_color}</code> - Cor do botão
                            </span>
                        </div>

                        <div class="cpr-setting-item">
                            <label class="cpr-setting-label">
                                <span class="dashicons dashicons-admin-settings"></span>
                                Usar Template Personalizado
                            </label>
                            <div class="cpr-toggle-wrapper">
                                <label class="cpr-toggle">
                                    <input type="checkbox" name="settings[use_custom_template]" id="use_custom_template" value="1" <?php checked(get_option('cnue_use_custom_template', '0'), '1'); ?>>
                                    <span class="cpr-toggle-slider"></span>
                                </label>
                                <span class="cpr-toggle-text">Usar o template HTML personalizado em vez do template padrão</span>
                            </div>
                            <span class="cpr-setting-desc">Ative para usar o template HTML personalizado que você editou acima</span>
                        </div>
                    </div>

                    <!-- Instruções e Ajuda -->
                    <div class="cpr-setting-group full-width">
                        <div class="cpr-setting-header">
                            <h3><span class="dashicons dashicons-editor-help"></span> Instruções de Uso</h3>
                        </div>

                        <div class="cpr-template-help">
                            <div class="cpr-help-section">
                                <h4><span class="dashicons dashicons-lightbulb"></span> Como usar o editor de template:</h4>
                                <ol>
                                    <li>Clique em "Carregar Template Padrão" para começar com o template atual</li>
                                    <li>Edite o HTML conforme necessário usando as variáveis disponíveis</li>
                                    <li>Use "Validar HTML" para verificar se o código está correto</li>
                                    <li>Ative "Usar Template Personalizado" para aplicar suas alterações</li>
                                    <li>Teste o email usando a função "Enviar Email de Teste"</li>
                                </ol>
                            </div>

                            <div class="cpr-help-section">
                                <h4><span class="dashicons dashicons-warning"></span> Importante:</h4>
                                <ul>
                                    <li>Sempre faça backup do seu template antes de fazer alterações</li>
                                    <li>Use apenas HTML válido para garantir compatibilidade com clientes de email</li>
                                    <li>Teste o template em diferentes clientes de email</li>
                                    <li>As variáveis serão substituídas automaticamente pelos valores reais</li>
                                </ul>
                            </div>

                            <div class="cpr-help-section">
                                <h4><span class="dashicons dashicons-admin-tools"></span> Dicas de CSS para Email:</h4>
                                <ul>
                                    <li>Use CSS inline sempre que possível</li>
                                    <li>Evite propriedades CSS3 avançadas</li>
                                    <li>Use tabelas para layout em vez de divs</li>
                                    <li>Teste em clientes como Gmail, Outlook, Apple Mail</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </form>

        <!-- Action Buttons -->
        <div class="cpr-admin-actions">
            <div class="cpr-actions-wrapper">
                <button type="button" id="save-settings" class="cpr-btn-primary">
                    <span class="dashicons dashicons-yes"></span>
                    Salvar Configurações
                </button>
                <button type="button" id="reset-settings" class="cpr-btn-secondary">
                    <span class="dashicons dashicons-update"></span>
                    Resetar Configurações
                </button>
                <button type="button" id="preview-email" class="cpr-btn-secondary">
                    <span class="dashicons dashicons-visibility"></span>
                    Preview Email
                </button>
                <div class="cpr-status-info">
                    <span class="cpr-status-label">Status:</span>
                    <span class="cpr-status-value" id="status-email-enabled">
                        <?php if ($settings['email_enabled']): ?>
                            <span class="cpr-status-dot active"></span> Ativo
                        <?php else: ?>
                            <span class="cpr-status-dot inactive"></span> Inativo
                        <?php endif; ?>
                    </span>
                </div>
            </div>
        </div>
    </div>


                </div>
            </div>
        </form>
    </div>
    </div>

    <!-- Loading Overlay -->
    <div id="cpr-loading" class="cpr-loading" style="display: none;">
        <div class="cpr-loading-content">
            <div class="cpr-spinner"></div>
            <p>Processando...</p>
        </div>
    </div>

    <!-- Modal Preview -->
    <div id="cpr-modal-overlay" class="cpr-modal-overlay">
        <div class="cpr-modal">
            <div class="cpr-modal-header">
                <h3 class="cpr-modal-title">
                    <span class="dashicons dashicons-visibility"></span>
                    Preview do Email
                </h3>
                <button type="button" class="cpr-modal-close" id="close-modal">
                    <span class="dashicons dashicons-no-alt"></span>
                </button>
            </div>
            <div class="cpr-modal-body">
                <div class="cpr-modal-preview" id="modal-preview-content">
                    <div class="cpr-preview-placeholder">
                        <div class="cpr-preview-icon">
                            <span class="dashicons dashicons-visibility" style="font-size: 48px; color: #ccc;"></span>
                        </div>
                        <p style="color: #666; text-align: center;">Carregando preview...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    const nonce = '<?php echo $nonce; ?>';
    
    // Inicializar color pickers
    $('.cpr-color-picker').wpColorPicker();

    // Gerenciar navegação por abas
    $('.cpr-nav-tab').on('click', function(e) {
        e.preventDefault();

        const targetTab = $(this).data('tab');

        // Remover classe ativa de todas as abas
        $('.cpr-nav-tab').removeClass('cpr-nav-tab-active');
        $('.cpr-tab-content').removeClass('active');

        // Adicionar classe ativa na aba clicada
        $(this).addClass('cpr-nav-tab-active');
        $('#tab-' + targetTab).addClass('active');
    });

    // Carregar template padrão
    $('#load-default-template').on('click', function() {
        if (confirm('Isso irá substituir o conteúdo atual do editor. Deseja continuar?')) {
            loadDefaultTemplate();
        }
    });

    // Resetar template
    $('#reset-template').on('click', function() {
        if (confirm('Isso irá limpar todo o conteúdo do editor. Deseja continuar?')) {
            $('#email_template_html').val('');
            showNotification('Template resetado com sucesso', 'success');
        }
    });

    // Validar template
    $('#validate-template').on('click', function() {
        validateTemplate();
    });

    // Salvar configurações
    $('#save-settings').on('click', function() {
        saveSettings(false);
    });
    
    // Resetar configurações
    $('#reset-settings').on('click', function() {
        if (confirm('Tem certeza que deseja resetar todas as configurações? Esta ação não pode ser desfeita.')) {
            resetSettings();
        }
    });
    

    
    // Enviar email de teste
    $('#send-test-email').on('click', function() {
        sendTestEmail();
    });

    // Preview email modal
    $('#preview-email').on('click', function() {
        openPreviewModal();
    });

    // Fechar modal
    $('#close-modal, #cpr-modal-overlay').on('click', function(e) {
        if (e.target === this) {
            closePreviewModal();
        }
    });

    // Fechar modal com ESC
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape') {
            closePreviewModal();
        }
    });

    // Atualizar status quando checkbox mudar
    $('#email_enabled').on('change', function() {
        const isEnabled = $(this).is(':checked');
        if (isEnabled) {
            $('#status-email-enabled').html('<span class="cpr-status-dot active"></span> Ativo');
        } else {
            $('#status-email-enabled').html('<span class="cpr-status-dot inactive"></span> Inativo');
        }
    });
    
    function saveSettings(isPreview = false) {
        showLoading();
        
        const settings = {};
        $('input, select, textarea').each(function() {
            const name = $(this).attr('name');
            if (name && name.startsWith('settings[')) {
                // Extrair o nome real da configuração
                const settingName = name.replace('settings[', '').replace(']', '');
                if ($(this).attr('type') === 'checkbox') {
                    settings[settingName] = $(this).is(':checked') ? '1' : '0';
                } else {
                    settings[settingName] = $(this).val();
                }
            }
        });
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'cnue_save_settings',
                nonce: nonce,
                settings: settings,
                preview: isPreview ? '1' : '0'
            },
            success: function(response) {
                hideLoading();
                if (response.success) {
                    showNotification(response.data, 'success');
                } else {
                    showNotification('Erro ao salvar: ' + response.data, 'error');
                }
            },
            error: function() {
                hideLoading();
                showNotification('Erro de conexão', 'error');
            }
        });
    }
    
    function resetSettings() {
        showLoading();
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'cnue_reset_settings',
                nonce: nonce
            },
            success: function(response) {
                hideLoading();
                if (response.success) {
                    showNotification(response.data, 'success');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showNotification('Erro ao resetar: ' + response.data, 'error');
                }
            },
            error: function() {
                hideLoading();
                showNotification('Erro de conexão', 'error');
            }
        });
    }
    

    
    function sendTestEmail() {
        const email = $('#test-email').val();
        if (!email) {
            showNotification('Por favor, insira um endereço de email', 'error');
            return;
        }
        
        showLoading();
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'cnue_send_test_email',
                nonce: nonce,
                email: email
            },
            success: function(response) {
                hideLoading();
                if (response.success) {
                    showNotification(response.data, 'success');
                } else {
                    showNotification('Erro ao enviar: ' + response.data, 'error');
                }
            },
            error: function() {
                hideLoading();
                showNotification('Erro de conexão', 'error');
            }
        });
    }
    
    function showLoading() {
        $('#cpr-loading').show();
    }
    
    function hideLoading() {
        $('#cpr-loading').hide();
    }
    
    function openPreviewModal() {
        showLoading();

        const settings = {};
        $('input, select, textarea').each(function() {
            const name = $(this).attr('name');
            if (name && name.startsWith('settings[')) {
                const settingName = name.replace('settings[', '').replace(']', '');
                if ($(this).attr('type') === 'checkbox') {
                    settings[settingName] = $(this).is(':checked') ? '1' : '0';
                } else {
                    settings[settingName] = $(this).val();
                }
            }
        });

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'cnue_generate_email_preview',
                nonce: nonce,
                settings: settings
            },
            success: function(response) {
                hideLoading();
                if (response.success) {
                    $('#modal-preview-content').html(response.data);
                    $('#cpr-modal-overlay').addClass('show');
                } else {
                    showNotification('Erro ao gerar preview: ' + response.data, 'error');
                }
            },
            error: function() {
                hideLoading();
                showNotification('Erro de conexão', 'error');
            }
        });
    }

    function closePreviewModal() {
        $('#cpr-modal-overlay').removeClass('show');
    }

    function loadDefaultTemplate() {
        showLoading();

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'cnue_load_default_template',
                nonce: nonce
            },
            success: function(response) {
                hideLoading();
                if (response.success) {
                    $('#email_template_html').val(response.data);
                    showNotification('Template padrão carregado com sucesso', 'success');
                } else {
                    showNotification('Erro ao carregar template: ' + response.data, 'error');
                }
            },
            error: function() {
                hideLoading();
                showNotification('Erro de conexão', 'error');
            }
        });
    }

    function validateTemplate() {
        const templateHtml = $('#email_template_html').val();

        if (!templateHtml.trim()) {
            showNotification('O template está vazio', 'error');
            return;
        }

        showLoading();

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'cnue_validate_template',
                nonce: nonce,
                template_html: templateHtml
            },
            success: function(response) {
                hideLoading();
                if (response.success) {
                    showNotification('Template HTML válido!', 'success');
                } else {
                    showNotification('Erro de validação: ' + response.data, 'error');
                }
            },
            error: function() {
                hideLoading();
                showNotification('Erro de conexão', 'error');
            }
        });
    }

    function showNotification(message, type) {
        const notification = $('<div class="cpr-notification cpr-notification-' + type + '">' + message + '</div>');
        $('body').append(notification);

        setTimeout(() => {
            notification.addClass('show');
        }, 100);

        setTimeout(() => {
            notification.removeClass('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
});
</script>
