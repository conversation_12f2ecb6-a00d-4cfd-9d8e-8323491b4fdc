/**
 * Custom Password Reset JavaScript
 */
(function($) {
    'use strict';

    class CustomPasswordReset {
        constructor() {
            this.currentTheme = localStorage.getItem('cpr-theme') || cprSettings.theme || 'dark';
            this.init();
        }

        init() {
            this.setupTheme();
            this.setupThemeToggle();
            this.setupFormValidation();
            this.setupFormSubmission();
            this.setupAnimations();
            this.setupAccessibility();
        }

        setupTheme() {
            // Apply theme to body
            document.body.classList.remove('cpr-theme-dark', 'cpr-theme-light');
            document.body.classList.add(`cpr-theme-${this.currentTheme}`);
            
            // Update CSS custom properties if needed
            this.updateThemeProperties();
        }

        updateThemeProperties() {
            // As cores agora são controladas via CSS dinâmico do PHP
            // Não precisamos mais sobrescrever as propriedades aqui
            // O CSS já aplica as cores corretas baseado na classe do tema
        }

        setupThemeToggle() {
            // Create theme toggle button if it doesn't exist
            if (!document.querySelector('.cpr-theme-toggle')) {
                const toggle = document.createElement('button');
                toggle.className = 'cpr-theme-toggle';
                toggle.setAttribute('aria-label', 'Alternar tema');
                toggle.innerHTML = this.currentTheme === 'dark' ? '<i class="fas fa-sun"></i>' : '<i class="fas fa-moon"></i>';
                document.body.appendChild(toggle);
            }

            // Handle theme toggle
            $(document).on('click', '.cpr-theme-toggle', (e) => {
                e.preventDefault();
                this.toggleTheme();
            });
        }

        toggleTheme() {
            this.currentTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
            localStorage.setItem('cpr-theme', this.currentTheme);
            
            this.setupTheme();
            
            // Update toggle icon
            const toggle = document.querySelector('.cpr-theme-toggle i');
            if (toggle) {
                toggle.className = this.currentTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }
            
            // Animate theme change (otimizado)
            document.body.style.transition = 'all 0.15s ease';
            setTimeout(() => {
                document.body.style.transition = '';
            }, 150);
        }

        setupFormValidation() {
            // Real-time validation
            $('.cpr-input').on('input blur', function() {
                const $input = $(this);
                const value = $input.val().trim();
                const type = $input.attr('type');
                
                $input.removeClass('cpr-input-error cpr-input-success');
                
                if (value) {
                    if (type === 'email' && !this.isValidEmail(value)) {
                        $input.addClass('cpr-input-error');
                        this.showFieldError($input, 'Por favor, insira um e-mail válido');
                    } else if (type === 'password' && value.length < 6) {
                        $input.addClass('cpr-input-error');
                        this.showFieldError($input, 'A senha deve ter pelo menos 6 caracteres');
                    } else {
                        $input.addClass('cpr-input-success');
                        this.hideFieldError($input);
                    }
                } else {
                    this.hideFieldError($input);
                }
            }.bind(this));

            // Password confirmation validation
            $('input[name="pass2"]').on('input blur', function() {
                const $confirm = $(this);
                const $password = $('input[name="pass1"]');
                const confirmValue = $confirm.val();
                const passwordValue = $password.val();
                
                $confirm.removeClass('cpr-input-error cpr-input-success');
                
                if (confirmValue) {
                    if (confirmValue !== passwordValue) {
                        $confirm.addClass('cpr-input-error');
                        this.showFieldError($confirm, 'As senhas não coincidem');
                    } else {
                        $confirm.addClass('cpr-input-success');
                        this.hideFieldError($confirm);
                    }
                } else {
                    this.hideFieldError($confirm);
                }
            }.bind(this));
        }

        isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        showFieldError($input, message) {
            this.hideFieldError($input);
            const errorDiv = $('<div class="cpr-field-error">' + message + '</div>');
            $input.after(errorDiv);
        }

        hideFieldError($input) {
            $input.siblings('.cpr-field-error').remove();
        }

        setupFormSubmission() {
            $('.cpr-form').on('submit', function(e) {
                const $form = $(this);
                const $button = $form.find('.cpr-button');
                
                // Prevent double submission
                if ($button.hasClass('cpr-loading')) {
                    e.preventDefault();
                    return false;
                }
                
                // Validate form
                if (!this.validateForm($form)) {
                    e.preventDefault();
                    return false;
                }
                
                // Show loading state
                this.showLoadingState($button);
                
                // Allow form to submit normally
                return true;
            }.bind(this));
        }

        validateForm($form) {
            let isValid = true;
            const $inputs = $form.find('.cpr-input[required]');
            
            $inputs.each(function() {
                const $input = $(this);
                const value = $input.val().trim();
                const type = $input.attr('type');
                
                if (!value) {
                    this.showFieldError($input, 'Este campo é obrigatório');
                    $input.addClass('cpr-input-error');
                    isValid = false;
                } else if (type === 'email' && !this.isValidEmail(value)) {
                    this.showFieldError($input, 'Por favor, insira um e-mail válido');
                    $input.addClass('cpr-input-error');
                    isValid = false;
                }
            }.bind(this));
            
            // Check password confirmation
            const $password = $form.find('input[name="pass1"]');
            const $confirm = $form.find('input[name="pass2"]');
            
            if ($password.length && $confirm.length) {
                if ($password.val() !== $confirm.val()) {
                    this.showFieldError($confirm, 'As senhas não coincidem');
                    $confirm.addClass('cpr-input-error');
                    isValid = false;
                }
            }
            
            return isValid;
        }

        showLoadingState($button) {
            const originalText = $button.text();
            $button.addClass('cpr-loading')
                   .prop('disabled', true)
                   .text('Enviando...');
            
            // Store original text for restoration
            $button.data('original-text', originalText);
        }

        hideLoadingState($button) {
            const originalText = $button.data('original-text') || 'Enviar';
            $button.removeClass('cpr-loading')
                   .prop('disabled', false)
                   .text(originalText);
        }

        setupAnimations() {
            // Remover animações para carregamento instantâneo
            $('.cpr-card').addClass('animate-in');
            
            // Remover delays das mensagens
            $('.cpr-message').css('animation-delay', '0s');
            
            // Scroll suave para erros (mantido mas mais rápido)
            if ($('.cpr-message-error').length) {
                $('html, body').animate({
                    scrollTop: $('.cpr-message-error').first().offset().top - 20
                }, 200);
            }
        }

        setupAccessibility() {
            // Add ARIA labels
            $('.cpr-input').each(function() {
                const $input = $(this);
                const $label = $input.siblings('.cpr-label');
                
                if ($label.length) {
                    const labelId = 'label-' + Math.random().toString(36).substr(2, 9);
                    $label.attr('id', labelId);
                    $input.attr('aria-labelledby', labelId);
                }
            });
            
            // Keyboard navigation for theme toggle
            $('.cpr-theme-toggle').on('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    $(this).click();
                }
            });
            
            // Announce theme changes to screen readers
            this.setupAriaLiveRegion();
        }

        setupAriaLiveRegion() {
            if (!document.getElementById('cpr-aria-live')) {
                const ariaLive = document.createElement('div');
                ariaLive.id = 'cpr-aria-live';
                ariaLive.setAttribute('aria-live', 'polite');
                ariaLive.setAttribute('aria-atomic', 'true');
                ariaLive.style.position = 'absolute';
                ariaLive.style.left = '-10000px';
                ariaLive.style.width = '1px';
                ariaLive.style.height = '1px';
                ariaLive.style.overflow = 'hidden';
                document.body.appendChild(ariaLive);
            }
        }

        announceToScreenReader(message) {
            const ariaLive = document.getElementById('cpr-aria-live');
            if (ariaLive) {
                ariaLive.textContent = message;
            }
        }

        // Public methods
        setTheme(theme) {
            if (['dark', 'light'].includes(theme)) {
                this.currentTheme = theme;
                localStorage.setItem('cpr-theme', theme);
                this.setupTheme();
            }
        }

        getTheme() {
            return this.currentTheme;
        }
    }

    // Initialize when document is ready
    $(document).ready(function() {
        window.customPasswordReset = new CustomPasswordReset();
        
        // Auto-hide messages removido para melhor UX
    });

    // Handle page visibility change to maintain theme
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden && window.customPasswordReset) {
            window.customPasswordReset.setupTheme();
        }
    });

})(jQuery);
