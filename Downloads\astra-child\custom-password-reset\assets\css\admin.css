/* Custom Password Reset Admin Styles - Modern & Clean */

.cpr-admin-wrap {
    max-width: 100%;
    margin: 0;
    padding: 0;
    background: #f8fafc;
    min-height: 100vh;
    /* Otimizações para evitar FOUC */
    opacity: 0;
    transition: opacity 0.3s ease;
    will-change: opacity;
}

.cpr-admin-wrap.loaded {
    opacity: 1;
}

/* Prevenção de layout shift */
.cpr-admin-wrap * {
    box-sizing: border-box;
}

/* Header moderno */
.cpr-admin-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 0;
    padding: 0;
    margin: 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.cpr-admin-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.08)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.12)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.cpr-header-content {
    position: relative;
    z-index: 2;
    padding: 40px 60px;
}

.cpr-header-title {
    display: flex;
    align-items: center;
    gap: 20px;
}

.cpr-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.cpr-icon .dashicons {
    font-size: 28px;
    color: white;
    width: 28px;
    height: 28px;
}

.cpr-title-text h1 {
    margin: 0 0 8px 0;
    font-size: 32px;
    font-weight: 700;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.cpr-title-text p {
    margin: 0;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 400;
}

/* Container principal */
.cpr-admin-container {
    background: white;
    border: none;
    border-radius: 0;
    margin: 0;
    box-shadow: none;
    overflow: hidden;
}

/* Navigation Tabs - Design moderno */
.cpr-nav-tabs {
    background: white;
    border-bottom: 1px solid #e2e8f0;
    margin: 0;
    padding: 0;
    position: sticky;
    top: 32px;
    z-index: 100;
}

.cpr-nav-wrapper {
    display: flex;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 60px;
    gap: 0;
}

.cpr-nav-tab {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 24px;
    text-decoration: none;
    color: #64748b;
    font-weight: 500;
    font-size: 14px;
    border-bottom: 3px solid transparent;
    transition: all 0.2s ease;
    position: relative;
    background: none;
    border-left: none;
    border-right: none;
    border-top: none;
}

.cpr-nav-tab:hover {
    color: #475569;
    background: #f8fafc;
    text-decoration: none;
}

.cpr-nav-tab.cpr-nav-tab-active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
    background: white;
}

.cpr-tab-icon {
    font-size: 16px;
    line-height: 1;
}

.cpr-tab-text {
    font-weight: 500;
}
/* Tab Content - Layout moderno */
.cpr-tab-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 60px;
    min-height: 600px;
    background: #f8fafc;
}

/* Grid de configurações */
.cpr-settings-grid {
    display: grid;
    gap: 32px;
}

.cpr-setting-group {
    background: white;
    border-radius: 12px;
    padding: 32px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.cpr-setting-header {
    margin-bottom: 32px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e2e8f0;
}

.cpr-setting-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Setting Items - Design moderno */
.cpr-setting-item {
    margin-bottom: 32px;
}

.cpr-setting-item:last-child {
    margin-bottom: 0;
}

.cpr-setting-label {
    display: block;
    font-weight: 600;
    font-size: 14px;
    color: #374151;
    margin-bottom: 8px;
}

.cpr-setting-desc {
    display: block;
    font-size: 13px;
    color: #6b7280;
    margin-top: 6px;
    line-height: 1.5;
}

/* Inputs modernos */
.cpr-input, .cpr-select {
    width: 100%;
    max-width: 400px;
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    transition: all 0.2s ease;
    color: #374151;
}

.cpr-input:focus, .cpr-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.cpr-input::placeholder {
    color: #9ca3af;
}

/* Input Group */
.cpr-input-group {
    display: flex;
    gap: 12px;
    align-items: center;
}

.cpr-input-group .cpr-input {
    flex: 1;
}

/* Toggle Switch moderno */
.cpr-toggle-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
}

.cpr-toggle {
    position: relative;
    display: inline-block;
    width: 48px;
    height: 24px;
}

.cpr-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.cpr-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #cbd5e1;
    transition: 0.3s;
    border-radius: 24px;
}

.cpr-toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.cpr-toggle input:checked + .cpr-toggle-slider {
    background-color: #3b82f6;
}

.cpr-toggle input:checked + .cpr-toggle-slider:before {
    transform: translateX(24px);
}

.cpr-toggle-text {
    font-size: 14px;
    color: #374151;
    font-weight: 500;
}

/* Conditional Fields */
.cpr-conditional {
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.cpr-conditional.show {
    display: block;
    opacity: 1;
}

/* Botões modernos */
.cpr-btn-primary, .cpr-btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;
    border: none;
    outline: none;
}

.cpr-btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.cpr-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
    color: white;
}

.cpr-btn-secondary {
    background: white;
    color: #374151;
    border: 1px solid #d1d5db;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.cpr-btn-secondary:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    transform: translateY(-1px);
    color: #374151;
}

.cpr-btn-icon {
    font-size: 16px;
    line-height: 1;
}

/* Action Buttons Container */
.cpr-admin-actions {
    background: white;
    border-top: 1px solid #e2e8f0;
    padding: 0;
    position: sticky;
    bottom: 0;
    z-index: 50;
}

.cpr-actions-wrapper {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px 60px;
    display: flex;
    align-items: center;
    gap: 16px;
}

/* Status Messages modernos */
.cpr-admin-status {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
    animation: slideIn 0.3s ease;
}

.cpr-admin-status.success {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.cpr-admin-status.error {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.cpr-admin-status::before {
    font-size: 16px;
    line-height: 1;
}

.cpr-admin-status.success::before {
    content: '✅';
}

.cpr-admin-status.error::before {
    content: '❌';
}

/* Loading Animation */
.dashicons.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Code Editor */
.code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.5;
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    padding: 15px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .cpr-header-content {
        padding: 30px 40px;
    }

    .cpr-nav-wrapper {
        padding: 0 40px;
    }

    .cpr-tab-content {
        padding: 40px;
    }

    .cpr-actions-wrapper {
        padding: 20px 40px;
    }
}

@media (max-width: 768px) {
    .cpr-header-content {
        padding: 20px;
    }

    .cpr-header-title {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }

    .cpr-nav-wrapper {
        padding: 0 20px;
        flex-wrap: wrap;
        gap: 0;
    }

    .cpr-nav-tab {
        flex: 1;
        min-width: 0;
        justify-content: center;
        padding: 12px 8px;
        font-size: 12px;
    }

    .cpr-tab-text {
        display: none;
    }

    .cpr-tab-content {
        padding: 20px;
    }

    .cpr-setting-group {
        padding: 20px;
    }

    .cpr-actions-wrapper {
        padding: 20px;
        flex-direction: column;
        gap: 12px;
    }

    .cpr-btn-primary, .cpr-btn-secondary {
        width: 100%;
        justify-content: center;
    }

    .cpr-input, .cpr-select {
        max-width: 100%;
    }

    .cpr-input-group {
        flex-direction: column;
        align-items: stretch;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .cpr-admin-container,
    .cpr-card {
        background: #1e1e1e;
        border-color: #3c3c3c;
        color: #e0e0e0;
    }
    
    .cpr-nav-tabs {
        background: #2a2a2a;
    }
    
    .cpr-nav-tabs .nav-tab.nav-tab-active {
        background: #1e1e1e;
        color: #4a9eff;
    }
    
    .cpr-card h2 {
        color: #e0e0e0;
        border-bottom-color: #3c3c3c;
    }
    
    .form-table .description {
        color: #b0b0b0;
    }
    
    .code {
        background: #2a2a2a;
        border-color: #3c3c3c;
        color: #e0e0e0;
    }
}

/* Accessibility */
.cpr-nav-tabs .nav-tab:focus {
    outline: 2px solid #0073aa;
    outline-offset: -2px;
}

.button:focus {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .cpr-nav-tabs .nav-tab.nav-tab-active {
        border-bottom-width: 4px;
    }
    
    .cpr-admin-status {
        border-width: 2px;
    }
}



/* Color Picker customizado */
.wp-picker-container {
    display: inline-block;
    /* Evitar layout shift durante inicialização */
    min-width: 40px;
    min-height: 40px;
}

.wp-picker-container .wp-color-result {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #d1d5db;
    height: 40px;
    width: 40px;
    /* Otimização para carregamento */
    transition: all 0.2s ease;
}

/* Placeholder para color picker antes da inicialização */
.cpr-color-picker:not(.wp-color-picker) {
    width: 40px;
    height: 40px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    cursor: pointer;
}

/* Upload Button */
.cpr-upload-button {
    margin-left: 0;
    vertical-align: top;
}

/* Animações suaves */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .cpr-btn-primary:hover,
    .cpr-btn-secondary:hover {
        transform: none;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .cpr-admin-wrap {
        background: #0f172a;
    }

    .cpr-setting-group {
        background: #1e293b;
        border-color: #334155;
        color: #e2e8f0;
    }

    .cpr-setting-header h3 {
        color: #f1f5f9;
    }

    .cpr-setting-label {
        color: #e2e8f0;
    }

    .cpr-setting-desc {
        color: #94a3b8;
    }

    .cpr-input, .cpr-select {
        background: #334155;
        border-color: #475569;
        color: #e2e8f0;
    }

    .cpr-input:focus, .cpr-select:focus {
        border-color: #3b82f6;
        background: #334155;
    }

    .cpr-toggle-slider {
        background-color: #475569;
    }

    .cpr-admin-actions {
        background: #1e293b;
        border-color: #334155;
    }
}

/* Ações do preview - Botão Ver ao vivo */
.cpr-preview-actions {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    gap: 16px !important;
    flex-wrap: wrap !important;
    margin-top: 16px !important;
    margin-bottom: 0 !important;
    padding: 16px !important;
    background: transparent !important;
    border-radius: 0 !important;
    border: none !important;
}

/* Botão Ver ao vivo - sempre clicável */
#cpr-open-external,
a#cpr-open-external,
.cpr-preview-actions #cpr-open-external {
    pointer-events: auto !important;
    cursor: pointer !important;
    text-decoration: none !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
    padding: 10px 20px !important;
    border-radius: 4px !important;
    font-weight: 500 !important;
    font-size: 14px !important;
    transition: all 0.15s ease !important;
    background: #ffffff !important;
    color: #495057 !important;
    border: 1px solid #ced4da !important;
    position: relative !important;
    z-index: 999 !important;
}

#cpr-open-external:hover,
a#cpr-open-external:hover,
.cpr-preview-actions #cpr-open-external:hover {
    text-decoration: none !important;
    background: #f8f9fa !important;
    border-color: #adb5bd !important;
    color: #495057 !important;
}

#cpr-open-external:focus {
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1) !important;
}

/* Ícone do botão */
#cpr-open-external .dashicons {
    font-size: 14px !important;
    line-height: 1 !important;
}/* Preview 
Container e Wrapper */
.cpr-preview-wrapper {
    margin-top: 20px !important;
}

.cpr-preview-container {
    background: #f8f9fa !important;
    border: 2px solid #e9ecef !important;
    border-radius: 8px !important;
    padding: 20px !important;
    position: relative !important;
    overflow: hidden !important;
    min-height: 600px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

#cpr-preview-frame {
    width: 100% !important;
    height: 600px !important;
    border: none !important;
    border-radius: 6px !important;
    background: #ffffff !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
}

/* Loading do preview */
.cpr-preview-loading {
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    display: none !important;
    align-items: center !important;
    gap: 8px !important;
    font-size: 14px !important;
    color: #6c757d !important;
    z-index: 10 !important;
    background: rgba(255, 255, 255, 0.9) !important;
    padding: 12px 20px !important;
    border-radius: 6px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.cpr-preview-loading.show {
    display: flex !important;
}

.cpr-preview-loading .dashicons {
    animation: spin 1s linear infinite !important;
}