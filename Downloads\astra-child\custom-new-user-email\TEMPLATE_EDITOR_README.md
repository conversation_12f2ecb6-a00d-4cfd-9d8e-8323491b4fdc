# Editor de Template de Email - Funcionalidades Implementadas

## Nova Aba "Template do Email"

Foi adicionada uma nova aba no painel administrativo do plugin que permite editar o template HTML do email de novo usuário.

### Funcionalidades Adicionadas:

#### 1. Editor de Template HTML
- **Campo de texto grande** para edição do HTML do template
- **Toolbar com botões**:
  - "Carregar Template Padrão" - Carrega um template HTML de exemplo
  - "Resetar Template" - <PERSON><PERSON> o editor
  - "Validar HTML" - Verifica se o HTML está válido

#### 2. Configuração de Uso
- **Toggle "Usar Template Personalizado"** - Ativa/desativa o uso do template personalizado
- Quando ativo, o template personalizado substitui o template padrão do plugin

#### 3. Variáveis Disponíveis
O template personalizado suporta as seguintes variáveis que são substituídas automaticamente:

- `{user_login}` - Nome de usuário
- `{user_email}` - <PERSON>ail do usuário  
- `{user_password}` - <PERSON>ha do usuário
- `{user_first_name}` - Primeiro nome do usuário
- `{site_name}` - Nome do site
- `{site_url}` - URL do site
- `{login_url}` - URL de login
- `{header_color}` - Cor do cabeçalho (das configurações)
- `{button_color}` - Cor do botão (das configurações)

#### 4. Validação e Segurança
- **Validação HTML** - Verifica se o HTML está bem formado
- **Sanitização** - Remove tags perigosas mantendo apenas HTML seguro para email
- **Backup automático** - As configurações originais são preservadas

#### 5. Instruções e Ajuda
- **Seção de instruções** com dicas de uso
- **Dicas de CSS para email** - Orientações sobre compatibilidade
- **Avisos importantes** sobre backup e testes

### Como Usar:

1. **Acesse** a aba "Template do Email" no painel do plugin
2. **Clique** em "Carregar Template Padrão" para começar com um exemplo
3. **Edite** o HTML conforme necessário usando as variáveis disponíveis
4. **Valide** o HTML usando o botão "Validar HTML"
5. **Ative** o toggle "Usar Template Personalizado"
6. **Salve** as configurações
7. **Teste** enviando um email de teste

### Integração com Sistema Existente:

- O template personalizado se integra perfeitamente com o sistema existente
- Todas as funcionalidades de preview e teste funcionam com o template personalizado
- As configurações de cores e outros elementos são aplicadas automaticamente
- O sistema volta automaticamente para o template padrão se o personalizado estiver vazio ou desativado

### Arquivos Modificados:

1. `templates/admin-page.php` - Adicionada nova aba e interface
2. `custom-new-user-email.php` - Adicionados novos métodos AJAX e lógica de processamento

### Métodos AJAX Adicionados:

- `cnue_load_default_template` - Carrega template de exemplo
- `cnue_validate_template` - Valida HTML do template
- Modificados métodos existentes para suportar template personalizado

### Configurações Adicionadas:

- `cnue_email_template_html` - Armazena o HTML do template personalizado
- `cnue_use_custom_template` - Controla se o template personalizado está ativo
