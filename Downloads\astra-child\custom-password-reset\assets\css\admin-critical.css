/* CSS Crítico para Admin - Carregamento Otimizado */

/* Prevenção de FOUC */
.cpr-admin-wrap {
    opacity: 0 !important;
    transition: opacity 0.2s ease !important;
    will-change: opacity !important;
}

.cpr-admin-wrap.loaded {
    opacity: 1 !important;
}

/* Layout básico para evitar layout shift */
.cpr-admin-header {
    min-height: 140px !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.cpr-nav-tabs {
    min-height: 60px !important;
    background: white !important;
    border-bottom: 1px solid #e2e8f0 !important;
}

.cpr-tab-content {
    min-height: 400px !important;
    background: #f8fafc !important;
}

.cpr-setting-group {
    min-height: 100px !important;
    background: white !important;
    border-radius: 12px !important;
}

/* Loading spinner crítico */
.cpr-loading {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    z-index: 9999 !important;
    background: rgba(255, 255, 255, 0.9) !important;
    padding: 20px !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.cpr-spinner {
    width: 32px !important;
    height: 32px !important;
    border: 3px solid #f3f3f3 !important;
    border-top: 3px solid #3b82f6 !important;
    border-radius: 50% !important;
    animation: cpr-spin 0.8s linear infinite !important;
}

@keyframes cpr-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Skeleton loading para elementos principais */
.cpr-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) !important;
    background-size: 200% 100% !important;
    animation: cpr-skeleton 1.5s infinite !important;
}

@keyframes cpr-skeleton {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Color picker placeholder */
.cpr-color-picker:not(.wp-color-picker) {
    width: 40px !important;
    height: 40px !important;
    border: 1px solid #d1d5db !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    background: #f8f9fa !important;
}