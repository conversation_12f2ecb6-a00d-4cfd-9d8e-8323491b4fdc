(()=>{var t={7604:(t,e,n)=>{var r;!function(){"use strict";var o={not_string:/[^s]/,not_bool:/[^t]/,not_type:/[^T]/,not_primitive:/[^v]/,number:/[diefg]/,numeric_arg:/[bcdiefguxX]/,json:/[j]/,not_json:/[^j]/,text:/^[^\x25]+/,modulo:/^\x25{2}/,placeholder:/^\x25(?:([1-9]\d*)\$|\(([^)]+)\))?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-gijostTuvxX])/,key:/^([a-z_][a-z_\d]*)/i,key_access:/^\.([a-z_][a-z_\d]*)/i,index_access:/^\[(\d+)\]/,sign:/^[+-]/};function i(t){return function(t,e){var n,r,s,a,c,l,u,p,d,f=1,h=t.length,m="";for(r=0;r<h;r++)if("string"==typeof t[r])m+=t[r];else if("object"==typeof t[r]){if((a=t[r]).keys)for(n=e[f],s=0;s<a.keys.length;s++){if(null==n)throw new Error(i('[sprintf] Cannot access property "%s" of undefined value "%s"',a.keys[s],a.keys[s-1]));n=n[a.keys[s]]}else n=a.param_no?e[a.param_no]:e[f++];if(o.not_type.test(a.type)&&o.not_primitive.test(a.type)&&n instanceof Function&&(n=n()),o.numeric_arg.test(a.type)&&"number"!=typeof n&&isNaN(n))throw new TypeError(i("[sprintf] expecting number but found %T",n));switch(o.number.test(a.type)&&(p=n>=0),a.type){case"b":n=parseInt(n,10).toString(2);break;case"c":n=String.fromCharCode(parseInt(n,10));break;case"d":case"i":n=parseInt(n,10);break;case"j":n=JSON.stringify(n,null,a.width?parseInt(a.width):0);break;case"e":n=a.precision?parseFloat(n).toExponential(a.precision):parseFloat(n).toExponential();break;case"f":n=a.precision?parseFloat(n).toFixed(a.precision):parseFloat(n);break;case"g":n=a.precision?String(Number(n.toPrecision(a.precision))):parseFloat(n);break;case"o":n=(parseInt(n,10)>>>0).toString(8);break;case"s":n=String(n),n=a.precision?n.substring(0,a.precision):n;break;case"t":n=String(!!n),n=a.precision?n.substring(0,a.precision):n;break;case"T":n=Object.prototype.toString.call(n).slice(8,-1).toLowerCase(),n=a.precision?n.substring(0,a.precision):n;break;case"u":n=parseInt(n,10)>>>0;break;case"v":n=n.valueOf(),n=a.precision?n.substring(0,a.precision):n;break;case"x":n=(parseInt(n,10)>>>0).toString(16);break;case"X":n=(parseInt(n,10)>>>0).toString(16).toUpperCase()}o.json.test(a.type)?m+=n:(!o.number.test(a.type)||p&&!a.sign?d="":(d=p?"+":"-",n=n.toString().replace(o.sign,"")),l=a.pad_char?"0"===a.pad_char?"0":a.pad_char.charAt(1):" ",u=a.width-(d+n).length,c=a.width&&u>0?l.repeat(u):"",m+=a.align?d+n+c:"0"===l?d+c+n:c+d+n)}return m}(function(t){if(a[t])return a[t];for(var e,n=t,r=[],i=0;n;){if(null!==(e=o.text.exec(n)))r.push(e[0]);else if(null!==(e=o.modulo.exec(n)))r.push("%");else{if(null===(e=o.placeholder.exec(n)))throw new SyntaxError("[sprintf] unexpected placeholder");if(e[2]){i|=1;var s=[],c=e[2],l=[];if(null===(l=o.key.exec(c)))throw new SyntaxError("[sprintf] failed to parse named argument key");for(s.push(l[1]);""!==(c=c.substring(l[0].length));)if(null!==(l=o.key_access.exec(c)))s.push(l[1]);else{if(null===(l=o.index_access.exec(c)))throw new SyntaxError("[sprintf] failed to parse named argument key");s.push(l[1])}e[2]=s}else i|=2;if(3===i)throw new Error("[sprintf] mixing positional and named placeholders is not (yet) supported");r.push({placeholder:e[0],param_no:e[1],keys:e[2],sign:e[3],pad_char:e[4],align:e[5],width:e[6],precision:e[7],type:e[8]})}n=n.substring(e[0].length)}return a[t]=r}(t),arguments)}function s(t,e){return i.apply(null,[t].concat(e||[]))}var a=Object.create(null);"undefined"!=typeof window&&(window.sprintf=i,window.vsprintf=s,void 0===(r=function(){return{sprintf:i,vsprintf:s}}.call(e,n,e,t))||(t.exports=r))}()}},e={};function n(r){var o=e[r];if(void 0!==o)return o.exports;var i=e[r]={exports:{}};return t[r](i,i.exports,n),i.exports}(()=>{"use strict";function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(e){var n=function(e,n){if("object"!=t(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,"string");if("object"!=t(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==t(n)?n:n+""}function r(t,n){for(var r=0;r<n.length;r++){var o=n[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,e(o.key),o)}}var o,i,s,a,c;n(7604),console.error,o=o||{},i={"(":9,"!":8,"*":7,"/":7,"%":7,"+":6,"-":6,"<":5,"<=":5,">":5,">=":5,"==":4,"!=":4,"&&":3,"||":2,"?":1,"?:":1},s=["(","?"],a={")":["("],":":["?","?:"]},c=/<=|>=|==|!=|&&|\|\||\?:|\(|!|\*|\/|%|\+|-|<|>|\?|\)|:/;var l={"!":function(t){return!t},"*":function(t,e){return t*e},"/":function(t,e){return t/e},"%":function(t,e){return t%e},"+":function(t,e){return t+e},"-":function(t,e){return t-e},"<":function(t,e){return t<e},"<=":function(t,e){return t<=e},">":function(t,e){return t>e},">=":function(t,e){return t>=e},"==":function(t,e){return t===e},"!=":function(t,e){return t!==e},"&&":function(t,e){return t&&e},"||":function(t,e){return t||e},"?:":function(t,e,n){if(t)throw e;return n}};var u={contextDelimiter:"",onMissingKey:null};function p(t,e){var n;for(n in this.data=t,this.pluralForms={},this.options={},u)this.options[n]=void 0!==e&&n in e?e[n]:u[n]}p.prototype.getPluralForm=function(t,e){var n,r,o,u,p=this.pluralForms[t];return p||("function"!=typeof(o=(n=this.data[t][""])["Plural-Forms"]||n["plural-forms"]||n.plural_forms)&&(r=function(t){var e,n,r;for(e=t.split(";"),n=0;n<e.length;n++)if(0===(r=e[n].trim()).indexOf("plural="))return r.substr(7)}(n["Plural-Forms"]||n["plural-forms"]||n.plural_forms),u=function(t){var e=function(t){for(var e,n,r,o,l=[],u=[];e=t.match(c);){for(n=e[0],(r=t.substr(0,e.index).trim())&&l.push(r);o=u.pop();){if(a[n]){if(a[n][0]===o){n=a[n][1]||n;break}}else if(s.indexOf(o)>=0||i[o]<i[n]){u.push(o);break}l.push(o)}a[n]||u.push(n),t=t.substr(e.index+n.length)}return(t=t.trim())&&l.push(t),l.concat(u.reverse())}(t);return function(t){return function(t,e){var n,r,o,i,s,a,c=[];for(n=0;n<t.length;n++){if(s=t[n],i=l[s]){for(r=i.length,o=Array(r);r--;)o[r]=c.pop();try{a=i.apply(null,o)}catch(t){return t}}else a=e.hasOwnProperty(s)?e[s]:+s;c.push(a)}return c[0]}(e,t)}}(r),o=function(t){return+u({n:t})}),p=this.pluralForms[t]=o),p(e)},p.prototype.dcnpgettext=function(t,e,n,r,o){var i,s,a;return i=void 0===o?0:this.getPluralForm(t,o),s=n,e&&(s=e+this.options.contextDelimiter+n),(a=this.data[t][s])&&a[i]?a[i]:(this.options.onMissingKey&&this.options.onMissingKey(n,t),0===i?n:r)};const d={plural_forms:t=>1===t?0:1},f=/^i18n\.(n?gettext|has_translation)(_|$)/,h=function(t){return"string"!=typeof t||""===t?(console.error("The namespace must be a non-empty string."),!1):!!/^[a-zA-Z][a-zA-Z0-9_.\-\/]*$/.test(t)||(console.error("The namespace can only contain numbers, letters, dashes, periods, underscores and slashes."),!1)},m=function(t){return"string"!=typeof t||""===t?(console.error("The hook name must be a non-empty string."),!1):/^__/.test(t)?(console.error("The hook name cannot begin with `__`."),!1):!!/^[a-zA-Z][a-zA-Z0-9_.-]*$/.test(t)||(console.error("The hook name can only contain numbers, letters, dashes, periods and underscores."),!1)},y=function(t,e){return function(n,r,o,i=10){const s=t[e];if(!m(n))return;if(!h(r))return;if("function"!=typeof o)return void console.error("The hook callback must be a function.");if("number"!=typeof i)return void console.error("If specified, the hook priority must be a number.");const a={callback:o,priority:i,namespace:r};if(s[n]){const t=s[n].handlers;let e;for(e=t.length;e>0&&!(i>=t[e-1].priority);e--);e===t.length?t[e]=a:t.splice(e,0,a),s.__current.forEach((t=>{t.name===n&&t.currentIndex>=e&&t.currentIndex++}))}else s[n]={handlers:[a],runs:0};"hookAdded"!==n&&t.doAction("hookAdded",n,r,o,i)}},g=function(t,e,n=!1){return function(r,o){const i=t[e];if(!m(r))return;if(!n&&!h(o))return;if(!i[r])return 0;let s=0;if(n)s=i[r].handlers.length,i[r]={runs:i[r].runs,handlers:[]};else{const t=i[r].handlers;for(let e=t.length-1;e>=0;e--)t[e].namespace===o&&(t.splice(e,1),s++,i.__current.forEach((t=>{t.name===r&&t.currentIndex>=e&&t.currentIndex--})))}return"hookRemoved"!==r&&t.doAction("hookRemoved",r,o),s}},_=function(t,e){return function(n,r){const o=t[e];return void 0!==r?n in o&&o[n].handlers.some((t=>t.namespace===r)):n in o}},b=function(t,e,n=!1){return function(r,...o){const i=t[e];i[r]||(i[r]={handlers:[],runs:0}),i[r].runs++;const s=i[r].handlers;if(!s||!s.length)return n?o[0]:void 0;const a={name:r,currentIndex:0};for(i.__current.push(a);a.currentIndex<s.length;){const t=s[a.currentIndex].callback.apply(null,o);n&&(o[0]=t),a.currentIndex++}return i.__current.pop(),n?o[0]:void 0}},v=function(t,e){return function(){var n;const r=t[e];return null!==(n=r.__current[r.__current.length-1]?.name)&&void 0!==n?n:null}},w=function(t,e){return function(n){const r=t[e];return void 0===n?void 0!==r.__current[0]:!!r.__current[0]&&n===r.__current[0].name}},x=function(t,e){return function(n){const r=t[e];if(m(n))return r[n]&&r[n].runs?r[n].runs:0}};class k{constructor(){this.actions=Object.create(null),this.actions.__current=[],this.filters=Object.create(null),this.filters.__current=[],this.addAction=y(this,"actions"),this.addFilter=y(this,"filters"),this.removeAction=g(this,"actions"),this.removeFilter=g(this,"filters"),this.hasAction=_(this,"actions"),this.hasFilter=_(this,"filters"),this.removeAllActions=g(this,"actions",!0),this.removeAllFilters=g(this,"filters",!0),this.doAction=b(this,"actions"),this.applyFilters=b(this,"filters",!0),this.currentAction=v(this,"actions"),this.currentFilter=v(this,"filters"),this.doingAction=w(this,"actions"),this.doingFilter=w(this,"filters"),this.didAction=x(this,"actions"),this.didFilter=x(this,"filters")}}const A=new k,{addAction:T,addFilter:j,removeAction:F,removeFilter:O,hasAction:S,hasFilter:E,removeAllActions:P,removeAllFilters:I,doAction:L,applyFilters:C,currentAction:N,currentFilter:M,doingAction:R,doingFilter:z,didAction:D,didFilter:U,actions:$,filters:H}=A,X=((t,e,n)=>{const r=new p({}),o=new Set,i=()=>{o.forEach((t=>t()))},s=(t,e="default")=>{r.data[e]={...r.data[e],...t},r.data[e][""]={...d,...r.data[e]?.[""]},delete r.pluralForms[e]},a=(t,e)=>{s(t,e),i()},c=(t="default",e,n,o,i)=>(r.data[t]||s(void 0,t),r.dcnpgettext(t,e,n,o,i)),l=(t="default")=>t,u=(t,e,r)=>{let o=c(r,e,t);return n?(o=n.applyFilters("i18n.gettext_with_context",o,t,e,r),n.applyFilters("i18n.gettext_with_context_"+l(r),o,t,e,r)):o};if(n){const t=t=>{f.test(t)&&i()};n.addAction("hookAdded","core/i18n",t),n.addAction("hookRemoved","core/i18n",t)}return{getLocaleData:(t="default")=>r.data[t],setLocaleData:a,addLocaleData:(t,e="default")=>{r.data[e]={...r.data[e],...t,"":{...d,...r.data[e]?.[""],...t?.[""]}},delete r.pluralForms[e],i()},resetLocaleData:(t,e)=>{r.data={},r.pluralForms={},a(t,e)},subscribe:t=>(o.add(t),()=>o.delete(t)),__:(t,e)=>{let r=c(e,void 0,t);return n?(r=n.applyFilters("i18n.gettext",r,t,e),n.applyFilters("i18n.gettext_"+l(e),r,t,e)):r},_x:u,_n:(t,e,r,o)=>{let i=c(o,void 0,t,e,r);return n?(i=n.applyFilters("i18n.ngettext",i,t,e,r,o),n.applyFilters("i18n.ngettext_"+l(o),i,t,e,r,o)):i},_nx:(t,e,r,o,i)=>{let s=c(i,o,t,e,r);return n?(s=n.applyFilters("i18n.ngettext_with_context",s,t,e,r,o,i),n.applyFilters("i18n.ngettext_with_context_"+l(i),s,t,e,r,o,i)):s},isRTL:()=>"rtl"===u("ltr","text direction"),hasTranslation:(t,e,o)=>{const i=e?e+""+t:t;let s=!!r.data?.[null!=o?o:"default"]?.[i];return n&&(s=n.applyFilters("i18n.has_translation",s,t,e,o),s=n.applyFilters("i18n.has_translation_"+l(o),s,t,e,o)),s}}})(0,0,A),q=(X.getLocaleData.bind(X),X.setLocaleData.bind(X),X.resetLocaleData.bind(X),X.subscribe.bind(X),X.__.bind(X));X._x.bind(X),X._n.bind(X),X._nx.bind(X),X.isRTL.bind(X),X.hasTranslation.bind(X);const J=(t,e)=>{let n,r,o=t.path;return"string"==typeof t.namespace&&"string"==typeof t.endpoint&&(n=t.namespace.replace(/^\/|\/$/g,""),r=t.endpoint.replace(/^\//,""),o=r?n+"/"+r:n),delete t.namespace,delete t.endpoint,e({...t,path:o})};function K(t){const e=t.split("?"),n=e[1],r=e[0];return n?r+"?"+n.split("&").map((t=>t.split("="))).map((t=>t.map(decodeURIComponent))).sort(((t,e)=>t[0].localeCompare(e[0]))).map((t=>t.map(encodeURIComponent))).map((t=>t.join("="))).join("&"):r}function Z(t){try{return decodeURIComponent(t)}catch(e){return t}}function G(t){return(function(t){let e;try{e=new URL(t,"http://example.com").search.substring(1)}catch(t){}if(e)return e}(t)||"").replace(/\+/g,"%20").split("&").reduce(((t,e)=>{const[n,r=""]=e.split("=").filter(Boolean).map(Z);return n&&function(t,e,n){const r=e.length,o=r-1;for(let i=0;i<r;i++){let r=e[i];!r&&Array.isArray(t)&&(r=t.length.toString()),r=["__proto__","constructor","prototype"].includes(r)?r.toUpperCase():r;const s=!isNaN(Number(e[i+1]));t[r]=i===o?n:t[r]||(s?[]:{}),Array.isArray(t[r])&&!s&&(t[r]={...t[r]}),t=t[r]}}(t,n.replace(/\]/g,"").split("["),r),t}),Object.create(null))}function B(t){let e="";const n=Object.entries(t);let r;for(;r=n.shift();){let[t,o]=r;if(Array.isArray(o)||o&&o.constructor===Object){const e=Object.entries(o).reverse();for(const[r,o]of e)n.unshift([`${t}[${r}]`,o])}else void 0!==o&&(null===o&&(o=""),e+="&"+[t,o].map(encodeURIComponent).join("="))}return e.substr(1)}function W(t="",e){if(!e||!Object.keys(e).length)return t;let n=t;const r=t.indexOf("?");return-1!==r&&(e=Object.assign(G(t),e),n=n.substr(0,r)),n+"?"+B(e)}function Y(t,e){return Promise.resolve(e?t.body:new window.Response(JSON.stringify(t.body),{status:200,statusText:"OK",headers:t.headers}))}const Q=({path:t,url:e,...n},r)=>({...n,url:e&&W(e,r),path:t&&W(t,r)}),V=t=>t.json?t.json():Promise.reject(t),tt=t=>{const{next:e}=(t=>{if(!t)return{};const e=t.match(/<([^>]+)>; rel="next"/);return e?{next:e[1]}:{}})(t.headers.get("link"));return e},et=async(t,e)=>{if(!1===t.parse)return e(t);if(!(t=>{const e=!!t.path&&-1!==t.path.indexOf("per_page=-1"),n=!!t.url&&-1!==t.url.indexOf("per_page=-1");return e||n})(t))return e(t);const n=await mt({...Q(t,{per_page:100}),parse:!1}),r=await V(n);if(!Array.isArray(r))return r;let o=tt(n);if(!o)return r;let i=[].concat(r);for(;o;){const e=await mt({...t,path:void 0,url:o,parse:!1}),n=await V(e);i=i.concat(n),o=tt(e)}return i},nt=new Set(["PATCH","PUT","DELETE"]),rt="GET";function ot(t,e){return G(t)[e]}function it(t,e){return void 0!==ot(t,e)}const st=(t,e=!0)=>Promise.resolve(((t,e=!0)=>e?204===t.status?null:t.json?t.json():Promise.reject(t):t)(t,e)).catch((t=>at(t,e)));function at(t,e=!0){if(!e)throw t;return(t=>{const e={code:"invalid_json",message:q("The response is not a valid JSON response.")};if(!t||!t.json)throw e;return t.json().catch((()=>{throw e}))})(t).then((t=>{const e={code:"unknown_error",message:q("An unknown error occurred.")};throw t||e}))}function ct(t,...e){const n=t.indexOf("?");if(-1===n)return t;const r=G(t),o=t.substr(0,n);e.forEach((t=>delete r[t]));const i=B(r);return i?o+"?"+i:o}const lt={Accept:"application/json, */*;q=0.1"},ut={credentials:"include"},pt=[(t,e)=>("string"!=typeof t.url||it(t.url,"_locale")||(t.url=W(t.url,{_locale:"user"})),"string"!=typeof t.path||it(t.path,"_locale")||(t.path=W(t.path,{_locale:"user"})),e(t)),J,(t,e)=>{const{method:n=rt}=t;return nt.has(n.toUpperCase())&&(t={...t,headers:{...t.headers,"X-HTTP-Method-Override":n,"Content-Type":"application/json"},method:"POST"}),e(t)},et],dt=t=>{if(t.status>=200&&t.status<300)return t;throw t};let ft=t=>{const{url:e,path:n,data:r,parse:o=!0,...i}=t;let{body:s,headers:a}=t;return a={...lt,...a},r&&(s=JSON.stringify(r),a["Content-Type"]="application/json"),window.fetch(e||n||window.location.href,{...ut,...i,body:s,headers:a}).then((t=>Promise.resolve(t).then(dt).catch((t=>at(t,o))).then((t=>st(t,o)))),(t=>{if(t&&"AbortError"===t.name)throw t;throw{code:"fetch_error",message:q("You are probably offline.")}}))};function ht(t){return pt.reduceRight(((t,e)=>n=>e(n,t)),ft)(t).catch((e=>"rest_cookie_invalid_nonce"!==e.code?Promise.reject(e):window.fetch(ht.nonceEndpoint).then(dt).then((t=>t.text())).then((e=>(ht.nonceMiddleware.nonce=e,ht(t))))))}ht.use=function(t){pt.unshift(t)},ht.setFetchHandler=function(t){ft=t},ht.createNonceMiddleware=function(t){const e=(t,n)=>{const{headers:r={}}=t;for(const o in r)if("x-wp-nonce"===o.toLowerCase()&&r[o]===e.nonce)return n(t);return n({...t,headers:{...r,"X-WP-Nonce":e.nonce}})};return e.nonce=t,e},ht.createPreloadingMiddleware=function(t){const e=Object.fromEntries(Object.entries(t).map((([t,e])=>[K(t),e])));return(t,n)=>{const{parse:r=!0}=t;let o=t.path;if(!o&&t.url){const{rest_route:e,...n}=G(t.url);"string"==typeof e&&(o=W(e,n))}if("string"!=typeof o)return n(t);const i=t.method||"GET",s=K(o);if("GET"===i&&e[s]){const t=e[s];return delete e[s],Y(t,!!r)}if("OPTIONS"===i&&e[i]&&e[i][s]){const t=e[i][s];return delete e[i][s],Y(t,!!r)}return n(t)}},ht.createRootURLMiddleware=t=>(e,n)=>J(e,(e=>{let r,o=e.url,i=e.path;return"string"==typeof i&&(r=t,-1!==t.indexOf("?")&&(i=i.replace("?","&")),i=i.replace(/^\//,""),"string"==typeof r&&-1!==r.indexOf("?")&&(i=i.replace("?","&")),o=r+i),n({...e,url:o})})),ht.fetchAllMiddleware=et,ht.mediaUploadMiddleware=(t,e)=>{if(!function(t){const e=!!t.method&&"POST"===t.method;return(!!t.path&&-1!==t.path.indexOf("/wp/v2/media")||!!t.url&&-1!==t.url.indexOf("/wp/v2/media"))&&e}(t))return e(t);let n=0;const r=t=>(n++,e({path:`/wp/v2/media/${t}/post-process`,method:"POST",data:{action:"create-image-subsizes"},parse:!1}).catch((()=>n<5?r(t):(e({path:`/wp/v2/media/${t}?force=true`,method:"DELETE"}),Promise.reject()))));return e({...t,parse:!1}).catch((e=>{const n=e.headers.get("x-wp-upload-attachment-id");return e.status>=500&&e.status<600&&n?r(n).catch((()=>!1!==t.parse?Promise.reject({code:"post_process",message:q("Media upload failed. If this is a photo or a large image, please scale it down and try again.")}):Promise.reject(e))):at(e,t.parse)})).then((e=>st(e,t.parse)))},ht.createThemePreviewMiddleware=t=>(e,n)=>{if("string"==typeof e.url){const n=ot(e.url,"wp_theme_preview");void 0===n?e.url=W(e.url,{wp_theme_preview:t}):""===n&&(e.url=ct(e.url,"wp_theme_preview"))}if("string"==typeof e.path){const n=ot(e.path,"wp_theme_preview");void 0===n?e.path=W(e.path,{wp_theme_preview:t}):""===n&&(e.path=ct(e.path,"wp_theme_preview"))}return n(e)};const mt=ht;var yt=UncannyAutomatorTooltipNotification.rest;mt.use(mt.createNonceMiddleware(yt.nonce));var gt=function(){return t=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.tooltips=document.querySelectorAll(".uap-tooltip-notification"),this.initializeTooltips()},e=[{key:"initializeTooltips",value:function(){var t=this;this.tooltips.forEach((function(e){t.initializeTooltip(e)}))}},{key:"initializeTooltip",value:function(t){var e=this,n=t.querySelector(".uap-tooltip-notification__close");this.positionTooltip(t),this.showTooltip(t),n.addEventListener("click",(function(n){n.preventDefault(),e.hideTooltip(t,t.dataset.id)}))}},{key:"showTooltip",value:function(t){t.classList.remove("uap-tooltip-notification--hidden")}},{key:"hideTooltip",value:function(t,e){t.classList.add("uap-tooltip-notification--hidden"),mt({url:"".concat(yt.url,"/tooltip-notification/tooltip_id/").concat(e,"/tooltip_action/hide/"),method:"POST"}).then((function(t){return console.log(t)})).catch((function(t){return console.warn(t)}))}},{key:"positionTooltip",value:function(t){var e=document.querySelector(t.dataset.target);if(e){e.insertAdjacentElement(t.dataset.position,t);var n=e.offsetHeight/2-t.offsetHeight/2;["beforebegin","afterend"].includes(t.dataset.position)&&(n+=e.offsetTop),t.style.top="".concat(n,"px")}}}],e&&r(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e}();document.addEventListener("DOMContentLoaded",(function(){new gt}))})()})();
//# sourceMappingURL=tooltip-notification.bundle.min.js.map