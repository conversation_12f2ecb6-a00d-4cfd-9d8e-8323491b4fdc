/* Custom Password Reset Styles - Cores dinâmicas serão injetadas via PHP */

/* Reset and base styles */
* {
    box-sizing: border-box;
}

body.cpr-password-reset {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: var(--cpr-primary-bg);
    color: var(--cpr-text-color);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

/* Main container */
.cpr-container {
    width: 100%;
    max-width: 400px;
    padding: 20px;
    margin: 0 auto;
}

/* Logo container - agora dentro do card */
.cpr-logo-container {
    text-align: center;
    margin-bottom: 30px;
}

.cpr-logo {
    max-width: 60px;
    height: auto;
    margin-bottom: 0;
}

.cpr-logo-icon {
    width: 60px;
    height: 60px;
    background: var(--cpr-button-bg);
    border-radius: 12px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0;
    box-shadow: 0 2px 10px rgba(74, 144, 226, 0.3);
}

.cpr-logo-icon i {
    font-size: 24px;
    color: var(--cpr-button-text);
}

/* Card */
.cpr-card {
    background: var(--cpr-card-bg);
    border-radius: var(--cpr-border-radius);
    border: var(--cpr-card-border);
    box-shadow: var(--cpr-card-shadow);
    padding: 40px 30px;
    text-align: center;
    transition: all 0.3s ease;
}

/* Typography */
.cpr-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--cpr-text-color);
    margin: 0 0 12px 0;
    line-height: 1.3;
}

.cpr-subtitle {
    font-size: 14px;
    color: var(--cpr-text-secondary);
    margin: 0 0 30px 0;
    line-height: 1.5;
}

/* Form styles */
.cpr-form {
    width: 100%;
}

.cpr-form-group {
    margin-bottom: 20px;
    text-align: left;
}

.cpr-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--cpr-text-color);
    margin-bottom: 8px;
}

.cpr-input,
.cpr-form .cpr-input,
input.cpr-input {
    width: 100%;
    padding: 14px 16px;
    font-size: 16px;
    border: 2px solid var(--cpr-input-border) !important;
    border-radius: var(--cpr-border-radius);
    background-color: var(--cpr-input-bg) !important;
    background: var(--cpr-input-bg) !important;
    color: var(--cpr-input-text) !important;
    transition: all 0.3s ease;
    outline: none;
}

.cpr-input:focus,
.cpr-form .cpr-input:focus,
input.cpr-input:focus {
    border-color: var(--cpr-input-focus) !important;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.cpr-input::placeholder {
    color: var(--cpr-input-placeholder);
}

/* Força aplicação das cores no tema dark */
.cpr-theme-dark .cpr-input,
body.cpr-theme-dark .cpr-input,
.cpr-theme-dark input.cpr-input,
body.cpr-theme-dark input.cpr-input,
.cpr-theme-dark .cpr-form .cpr-input,
body.cpr-theme-dark .cpr-form .cpr-input {
    background-color: var(--cpr-input-bg) !important;
    background: var(--cpr-input-bg) !important;
    color: var(--cpr-input-text) !important;
    border: 2px solid var(--cpr-input-border) !important;
    border-color: var(--cpr-input-border) !important;
}

.cpr-theme-dark .cpr-input:focus,
body.cpr-theme-dark .cpr-input:focus,
.cpr-theme-dark input.cpr-input:focus,
body.cpr-theme-dark input.cpr-input:focus,
.cpr-theme-dark .cpr-form .cpr-input:focus,
body.cpr-theme-dark .cpr-form .cpr-input:focus {
    border-color: var(--cpr-input-focus) !important;
    border: 2px solid var(--cpr-input-focus) !important;
}

/* Força aplicação das cores no tema light */
.cpr-theme-light .cpr-input,
body.cpr-theme-light .cpr-input,
.cpr-theme-light input.cpr-input,
body.cpr-theme-light input.cpr-input,
.cpr-theme-light .cpr-form .cpr-input,
body.cpr-theme-light .cpr-form .cpr-input {
    background-color: var(--cpr-input-bg) !important;
    background: var(--cpr-input-bg) !important;
    color: var(--cpr-input-text) !important;
    border: 2px solid var(--cpr-input-border) !important;
    border-color: var(--cpr-input-border) !important;
}

.cpr-theme-light .cpr-input:focus,
body.cpr-theme-light .cpr-input:focus,
.cpr-theme-light input.cpr-input:focus,
body.cpr-theme-light input.cpr-input:focus,
.cpr-theme-light .cpr-form .cpr-input:focus,
body.cpr-theme-light .cpr-form .cpr-input:focus {
    border-color: var(--cpr-input-focus) !important;
    border: 2px solid var(--cpr-input-focus) !important;
}

/* Força aplicação do hover do botão no tema dark */
.cpr-theme-dark .cpr-button:hover,
body.cpr-theme-dark .cpr-button:hover {
    background-color: var(--cpr-button-hover) !important;
}

/* Força aplicação do hover do botão no tema light */
.cpr-theme-light .cpr-button:hover,
body.cpr-theme-light .cpr-button:hover {
    background-color: var(--cpr-button-hover) !important;
}

/* Força borda em TODOS os estados - máxima especificidade */
.cpr-input,
.cpr-input:not(:focus),
.cpr-input:hover,
.cpr-input:active,
input.cpr-input,
input.cpr-input:not(:focus),
input.cpr-input:hover,
input.cpr-input:active {
    border-color: var(--cpr-input-border) !important;
    border-width: 2px !important;
    border-style: solid !important;
}

.cpr-input:focus,
input.cpr-input:focus {
    border-color: var(--cpr-input-focus) !important;
    border-width: 2px !important;
    border-style: solid !important;
}

/* Button styles */
.cpr-button,
.cpr-form .cpr-button,
button.cpr-button {
    width: 100%;
    padding: 14px 20px;
    font-size: 16px;
    font-weight: 600;
    border: none;
    border-radius: var(--cpr-border-radius);
    background-color: var(--cpr-button-bg) !important;
    color: var(--cpr-button-text) !important;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 20px;
    text-transform: none;
    letter-spacing: 0;
    text-align: center;
}

.cpr-button:hover,
.cpr-form .cpr-button:hover,
button.cpr-button:hover {
    background-color: var(--cpr-button-hover) !important;
}

.cpr-button:active {
    /* Removido transform para eliminar animação */
}

.cpr-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}



/* Secondary button */
.cpr-button-secondary {
    background: transparent;
    color: var(--cpr-text-secondary);
    border: 2px solid var(--cpr-input-border);
    font-weight: 500;
}

.cpr-button-secondary:hover {
    background: var(--cpr-input-bg);
    color: var(--cpr-text-color);
    border-color: var(--cpr-input-focus);
    box-shadow: none;
}

/* Links */
.cpr-link {
    color: var(--cpr-button-bg);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.cpr-link:hover {
    color: var(--cpr-button-hover);
    text-decoration: underline;
}

/* Messages */
.cpr-message {
    padding: 12px 16px;
    border-radius: var(--cpr-border-radius);
    margin-bottom: 20px;
    font-size: 14px;
    font-weight: 500;
    text-align: left;
}

.cpr-message-error {
    background-color: rgba(255, 107, 107, 0.1);
    border: 1px solid var(--cpr-error-color);
    color: var(--cpr-error-color);
}

.cpr-message-success {
    background-color: rgba(81, 207, 102, 0.1);
    border: 1px solid var(--cpr-success-color);
    color: var(--cpr-success-color);
}

/* Footer */
.cpr-footer {
    text-align: center;
    margin-top: 30px;
}

.cpr-footer-text {
    font-size: 12px;
    color: var(--cpr-text-secondary);
    margin: 0;
}

/* Loading state */
.cpr-loading {
    position: relative;
    overflow: hidden;
}

.cpr-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Theme toggle */
.cpr-theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--cpr-card-bg);
    border: 2px solid var(--cpr-input-border);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cpr-theme-toggle:hover {
    border-color: var(--cpr-input-focus);
    transform: scale(1.05);
}

.cpr-theme-toggle i {
    font-size: 20px;
    color: var(--cpr-text-color);
}

/* Responsive design */
@media (max-width: 480px) {
    .cpr-container {
        padding: 15px;
    }
    
    .cpr-card {
        padding: 30px 20px;
    }
    
    .cpr-title {
        font-size: 22px;
    }
    
    .cpr-subtitle {
        font-size: 13px;
    }
    
    .cpr-input,
    .cpr-button {
        font-size: 16px; /* Prevent zoom on iOS */
    }
    
    .cpr-theme-toggle {
        top: 15px;
        right: 15px;
        width: 45px;
        height: 45px;
    }
}

/* Animation for form appearance - REMOVIDO para eliminar delay */
.cpr-card {
    /* Animação removida para carregamento instantâneo */
    opacity: 1 !important;
    transform: translateY(0) !important;
    visibility: visible !important;
    display: block !important;
}

/* Garantir que o container também apareça imediatamente */
.cpr-container {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Garantir que o body apareça imediatamente */
body.cpr-password-reset {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Focus visible for accessibility */
.cpr-button:focus-visible,
.cpr-input:focus-visible,
.cpr-theme-toggle:focus-visible {
    outline: 2px solid var(--cpr-input-focus);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --cpr-input-border: #000000;
        --cpr-text-secondary: #000000;
    }
    
    .cpr-theme-light {
        --cpr-input-border: #000000;
        --cpr-text-secondary: #000000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
